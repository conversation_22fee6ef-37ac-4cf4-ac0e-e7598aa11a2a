pygame 2.6.1 (SDL 2.28.4, Python 3.12.0)
Hello from the pygame community. https://www.pygame.org/contribute.html
pygame 2.6.1 (SDL 2.28.4, Python 3.12.0)
Hello from the pygame community. https://www.pygame.org/contribute.html
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/Documents/GitHub/NizhamPygameZero/BasicPGZ/.venv/BasicPGZ/lib/python3.12/site-packages/pgzero/__main__.py", line 3, in <module>
    main()
  File "/Users/<USER>/Documents/GitHub/NizhamPygameZero/BasicPGZ/.venv/BasicPGZ/lib/python3.12/site-packages/pgzero/runner.py", line 93, in main
    run_mod(mod)
  File "/Users/<USER>/Documents/GitHub/NizhamPygameZero/BasicPGZ/.venv/BasicPGZ/lib/python3.12/site-packages/pgzero/runner.py", line 113, in run_mod
    PGZeroGame(mod).run()
  File "/Users/<USER>/Documents/GitHub/NizhamPygameZero/BasicPGZ/.venv/BasicPGZ/lib/python3.12/site-packages/pgzero/game.py", line 217, in run
    self.mainloop()
  File "/Users/<USER>/Documents/GitHub/NizhamPygameZero/BasicPGZ/.venv/BasicPGZ/lib/python3.12/site-packages/pgzero/game.py", line 225, in mainloop
    self.reinit_screen()
  File "/Users/<USER>/Documents/GitHub/NizhamPygameZero/BasicPGZ/.venv/BasicPGZ/lib/python3.12/site-packages/pgzero/game.py", line 73, in reinit_screen
    self.mod.screen.surface = self.screen
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'surface'
